# Kalite <PERSON>

## 🎯 <PERSON><PERSON>

### Genel Kali<PERSON> Metrikleri
- **Code Quality Score:** A+ (SonarQube)
- **Test Coverage:** %90+ overall
- **Performance Score:** 90+ (Lighthouse)
- **Accessibility Score:** AA compliance (WCAG 2.1)
- **Security Score:** A+ (OWASP)

### Teknik Borç <PERSON>
- **Technical Debt Ratio:** <5%
- **Code Duplication:** <3%
- **Cyclomatic Complexity:** <10 per function
- **Maintainability Index:** >70

## 📏 Code Quality Standards

### TypeScript Standards
```typescript
// tsconfig.json - Strict Configuration
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true
  }
}
```

### ESLint Configuration
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    '@typescript-eslint/recommended-requiring-type-checking',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended'
  ],
  rules: {
    // Code Quality
    'complexity': ['error', 10],
    'max-depth': ['error', 4],
    'max-lines': ['error', 300],
    'max-lines-per-function': ['error', 50],
    'max-params': ['error', 4],
    
    // TypeScript Specific
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/prefer-readonly': 'error',
    '@typescript-eslint/prefer-nullish-coalescing': 'error',
    
    // React Specific
    'react/prop-types': 'off',
    'react/react-in-jsx-scope': 'off',
    'react-hooks/exhaustive-deps': 'error',
    
    // Import/Export
    'import/order': ['error', {
      'groups': ['builtin', 'external', 'internal', 'parent', 'sibling'],
      'newlines-between': 'always'
    }]
  }
}
```

### Prettier Configuration
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 🏗️ Architecture Standards

### Clean Architecture Principles
```typescript
// Domain Layer - No external dependencies
export class Project {
  constructor(
    private readonly id: ProjectId,
    private name: string,
    private layers: Layer[]
  ) {}
  
  // Business logic only
  addLayer(layer: Layer): void {
    if (this.layers.length >= 50) {
      throw new DomainError('Maximum 50 layers allowed')
    }
    this.layers.push(layer)
  }
}

// Application Layer - Orchestrates domain
export class CreateProjectUseCase {
  constructor(
    private readonly projectRepo: ProjectRepository,
    private readonly eventBus: EventBus
  ) {}
  
  async execute(command: CreateProjectCommand): Promise<void> {
    const project = new Project(
      new ProjectId(generateId()),
      command.name,
      []
    )
    
    await this.projectRepo.save(project)
    await this.eventBus.publish(new ProjectCreatedEvent(project.id))
  }
}
```

### SOLID Principles Implementation
```typescript
// Single Responsibility Principle
export class RarityCalculator {
  calculate(traits: Trait[]): number {
    return traits.reduce((sum, trait) => sum + trait.rarity, 0)
  }
}

// Open/Closed Principle
export interface ExportStrategy {
  export(nfts: NFT[]): Promise<void>
}

export class PNGExportStrategy implements ExportStrategy {
  async export(nfts: NFT[]): Promise<void> {
    // PNG specific export logic
  }
}

// Liskov Substitution Principle
export abstract class BaseRepository<T> {
  abstract save(entity: T): Promise<void>
  abstract findById(id: string): Promise<T | null>
}

// Interface Segregation Principle
export interface Readable<T> {
  findById(id: string): Promise<T | null>
}

export interface Writable<T> {
  save(entity: T): Promise<void>
}

// Dependency Inversion Principle
export class ProjectService {
  constructor(
    private readonly projectRepo: ProjectRepository, // Interface
    private readonly logger: Logger // Interface
  ) {}
}
```

## 🧪 Testing Standards

### Test Coverage Requirements
```typescript
// jest.config.js
module.exports = {
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './src/domain/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/application/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
}
```

### Test Quality Standards
```typescript
// Good Test Example
describe('ProjectService', () => {
  let projectService: ProjectService
  let mockProjectRepo: jest.Mocked<ProjectRepository>
  let mockEventBus: jest.Mocked<EventBus>
  
  beforeEach(() => {
    mockProjectRepo = createMockProjectRepository()
    mockEventBus = createMockEventBus()
    projectService = new ProjectService(mockProjectRepo, mockEventBus)
  })
  
  describe('createProject', () => {
    it('should create project with valid data', async () => {
      // Arrange
      const command = new CreateProjectCommand('Test Project', 'Description')
      
      // Act
      await projectService.createProject(command)
      
      // Assert
      expect(mockProjectRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Project'
        })
      )
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.any(ProjectCreatedEvent)
      )
    })
    
    it('should throw error when name is empty', async () => {
      // Arrange
      const command = new CreateProjectCommand('', 'Description')
      
      // Act & Assert
      await expect(projectService.createProject(command))
        .rejects.toThrow('Project name cannot be empty')
    })
  })
})
```

## 🚀 Performance Standards

### Bundle Size Limits
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@mui/material'],
          utils: ['zustand', 'lodash']
        }
      }
    }
  },
  
  // Bundle size warnings
  build: {
    chunkSizeWarningLimit: 1000 // 1MB
  }
})
```

### Performance Budgets
```json
{
  "budgets": [
    {
      "type": "initial",
      "maximumWarning": "2mb",
      "maximumError": "5mb"
    },
    {
      "type": "anyComponentStyle",
      "maximumWarning": "6kb"
    }
  ]
}
```

### Core Web Vitals Targets
- **Largest Contentful Paint (LCP):** < 2.5s
- **First Input Delay (FID):** < 100ms
- **Cumulative Layout Shift (CLS):** < 0.1
- **First Contentful Paint (FCP):** < 1.8s
- **Time to Interactive (TTI):** < 3.8s

## ♿ Accessibility Standards

### WCAG 2.1 AA Compliance
```typescript
// Accessibility Testing
describe('Accessibility', () => {
  it('should have no accessibility violations', async () => {
    const { container } = render(<App />)
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })
})
```

### Accessibility Checklist
- [ ] **Keyboard Navigation** - All interactive elements accessible via keyboard
- [ ] **Screen Reader Support** - Proper ARIA labels and roles
- [ ] **Color Contrast** - Minimum 4.5:1 ratio for normal text
- [ ] **Focus Management** - Clear focus indicators
- [ ] **Alternative Text** - All images have descriptive alt text
- [ ] **Form Labels** - All form inputs have associated labels
- [ ] **Heading Structure** - Logical heading hierarchy
- [ ] **Error Messages** - Clear and descriptive error messages

## 🔒 Security Standards

### Security Checklist
```typescript
// Input Validation
export const validateFileUpload = (file: File): ValidationResult => {
  const allowedTypes = ['image/png', 'image/jpeg', 'image/webp']
  const maxSize = 50 * 1024 * 1024 // 50MB
  
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: 'Invalid file type' }
  }
  
  if (file.size > maxSize) {
    return { isValid: false, error: 'File too large' }
  }
  
  return { isValid: true }
}

// XSS Prevention
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '')
    .trim()
    .substring(0, 1000)
}

// Path Traversal Prevention
export const validatePath = (path: string): boolean => {
  return !path.includes('..') && !path.includes('~')
}
```

### Security Headers
```typescript
const securityHeaders = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'",
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
}
```

## 📊 Code Quality Metrics

### SonarQube Configuration
```properties
# sonar-project.properties
sonar.projectKey=nft-generator-pro-v2
sonar.organization=nft-generator
sonar.sources=src
sonar.tests=src
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.ts,**/*.test.tsx,**/*.stories.tsx
```

### Quality Gates
```yaml
# Quality Gate Conditions
- Coverage: > 90%
- Duplicated Lines: < 3%
- Maintainability Rating: A
- Reliability Rating: A
- Security Rating: A
- Technical Debt: < 5%
- Code Smells: < 10
- Bugs: 0
- Vulnerabilities: 0
```

## 🔄 Continuous Quality

### Pre-commit Hooks
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

### Commit Message Standards
```
type(scope): description

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation
- style: Formatting
- refactor: Code restructuring
- test: Adding tests
- chore: Maintenance

Examples:
feat(layers): add drag and drop reordering
fix(traits): resolve rarity calculation bug
docs(api): update component documentation
```

### Code Review Checklist
- [ ] **Functionality** - Code works as intended
- [ ] **Tests** - Adequate test coverage
- [ ] **Performance** - No performance regressions
- [ ] **Security** - No security vulnerabilities
- [ ] **Accessibility** - Meets accessibility standards
- [ ] **Documentation** - Code is well documented
- [ ] **Standards** - Follows coding standards
- [ ] **Architecture** - Follows architectural principles
