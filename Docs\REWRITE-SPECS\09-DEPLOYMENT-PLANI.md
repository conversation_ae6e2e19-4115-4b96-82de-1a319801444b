# Deployment Planı

## 🎯 Deployment Stratejisi

### Yaklaşım
- **Static Site Deployment** - SPA olarak deploy
- **Multi-Environment** - Dev, Staging, Production
- **Blue-Green Deployment** - Zero downtime deployment
- **Progressive Rollout** - Gradual feature release

### Hedefler
- **Zero Downtime** - Kesintisiz deployment
- **Fast Rollback** - Hızlı geri alma
- **Automated Pipeline** - Otomatik deployment
- **Environment Parity** - <PERSON><PERSON> tutarlılığı

## 🏗️ Build Stratejisi

### Build Configuration
```typescript
// vite.config.ts
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  
  build: {
    outDir: 'dist',
    sourcemap: mode === 'development',
    minify: mode === 'production' ? 'esbuild' : false,
    
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@mui/material', '@emotion/react'],
          utils: ['zustand', '@tanstack/react-query']
        }
      }
    },
    
    chunkSizeWarningLimit: 1000
  },
  
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __ENVIRONMENT__: JSON.stringify(mode)
  }
}))
```

### Environment Configuration
```typescript
// src/config/environment.ts
interface EnvironmentConfig {
  apiUrl: string
  enableAnalytics: boolean
  enableDebug: boolean
  maxFileSize: number
  supportedFormats: string[]
}

const environments: Record<string, EnvironmentConfig> = {
  development: {
    apiUrl: 'http://localhost:3001',
    enableAnalytics: false,
    enableDebug: true,
    maxFileSize: 100 * 1024 * 1024, // 100MB
    supportedFormats: ['png', 'jpg', 'jpeg', 'webp', 'tiff']
  },
  
  staging: {
    apiUrl: 'https://api-staging.nftgenerator.com',
    enableAnalytics: true,
    enableDebug: true,
    maxFileSize: 50 * 1024 * 1024, // 50MB
    supportedFormats: ['png', 'jpg', 'jpeg', 'webp']
  },
  
  production: {
    apiUrl: 'https://api.nftgenerator.com',
    enableAnalytics: true,
    enableDebug: false,
    maxFileSize: 50 * 1024 * 1024, // 50MB
    supportedFormats: ['png', 'jpg', 'jpeg', 'webp']
  }
}

export const config = environments[import.meta.env.MODE] || environments.development
```

## 🌐 Hosting Stratejisi

### Primary Hosting (Vercel)
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    },
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### Alternative Hosting (Netlify)
```toml
# netlify.toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
```

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test:ci
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [staging, production]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for ${{ matrix.environment }}
        run: npm run build:${{ matrix.environment }}
        env:
          NODE_ENV: ${{ matrix.environment }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-${{ matrix.environment }}
          path: dist/

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-staging
          path: dist/
      
      - name: Deploy to Staging
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-production
          path: dist/
      
      - name: Deploy to Production
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
```

### Build Scripts
```json
{
  "scripts": {
    "build": "vite build",
    "build:staging": "vite build --mode staging",
    "build:production": "vite build --mode production",
    "preview": "vite preview",
    "deploy:staging": "npm run build:staging && vercel --target staging",
    "deploy:production": "npm run build:production && vercel --prod"
  }
}
```

## 🔧 Environment Management

### Environment Variables
```bash
# .env.development
VITE_APP_NAME=NFT Generator Pro V2
VITE_APP_VERSION=2.0.0
VITE_API_URL=http://localhost:3001
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true
VITE_MAX_FILE_SIZE=104857600
VITE_SENTRY_DSN=

# .env.staging
VITE_APP_NAME=NFT Generator Pro V2 (Staging)
VITE_APP_VERSION=2.0.0-staging
VITE_API_URL=https://api-staging.nftgenerator.com
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=true
VITE_MAX_FILE_SIZE=52428800
VITE_SENTRY_DSN=https://<EMAIL>/project

# .env.production
VITE_APP_NAME=NFT Generator Pro V2
VITE_APP_VERSION=2.0.0
VITE_API_URL=https://api.nftgenerator.com
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false
VITE_MAX_FILE_SIZE=52428800
VITE_SENTRY_DSN=https://<EMAIL>/project
```

### Secret Management
```yaml
# GitHub Secrets
VERCEL_TOKEN: # Vercel deployment token
VERCEL_ORG_ID: # Vercel organization ID
VERCEL_PROJECT_ID: # Vercel project ID
SENTRY_AUTH_TOKEN: # Sentry authentication token
CODECOV_TOKEN: # Code coverage token
```

## 📊 Monitoring & Analytics

### Error Tracking (Sentry)
```typescript
// src/utils/monitoring.ts
import * as Sentry from '@sentry/react'
import { BrowserTracing } from '@sentry/tracing'

export const initializeMonitoring = () => {
  if (config.enableAnalytics && config.sentryDsn) {
    Sentry.init({
      dsn: config.sentryDsn,
      environment: import.meta.env.MODE,
      integrations: [
        new BrowserTracing({
          routingInstrumentation: Sentry.reactRouterV6Instrumentation(
            React.useEffect,
            useLocation,
            useNavigationType,
            createRoutesFromChildren,
            matchRoutes
          )
        })
      ],
      tracesSampleRate: 0.1,
      beforeSend: (event) => {
        // Filter out development errors
        if (import.meta.env.MODE === 'development') {
          return null
        }
        return event
      }
    })
  }
}
```

### Performance Monitoring
```typescript
// src/utils/performance.ts
export const trackPerformance = () => {
  // Core Web Vitals
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    getCLS(console.log)
    getFID(console.log)
    getFCP(console.log)
    getLCP(console.log)
    getTTFB(console.log)
  })
  
  // Custom metrics
  performance.mark('app-start')
  
  window.addEventListener('load', () => {
    performance.mark('app-loaded')
    performance.measure('app-load-time', 'app-start', 'app-loaded')
    
    const measure = performance.getEntriesByName('app-load-time')[0]
    console.log('App load time:', measure.duration)
  })
}
```

## 🔄 Rollback Strategy

### Automated Rollback
```yaml
# .github/workflows/rollback.yml
name: Rollback Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        default: 'production'
        type: choice
        options:
        - staging
        - production
      version:
        description: 'Version to rollback to'
        required: true
        type: string

jobs:
  rollback:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Rollback to version ${{ github.event.inputs.version }}
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-args: '--rollback ${{ github.event.inputs.version }}'
```

### Manual Rollback Process
1. **Identify Issue** - Monitor alerts, user reports
2. **Assess Impact** - Determine severity and scope
3. **Execute Rollback** - Use automated workflow or manual process
4. **Verify Rollback** - Confirm system stability
5. **Communicate** - Notify stakeholders
6. **Post-Mortem** - Analyze root cause

## 🔒 Security Considerations

### Content Security Policy
```html
<!-- index.html -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval';
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: blob:;
  connect-src 'self' https://api.nftgenerator.com;
  worker-src 'self' blob:;
">
```

### Security Headers
```typescript
// Security headers configuration
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
}
```

## 📈 Performance Optimization

### Bundle Analysis
```json
{
  "scripts": {
    "analyze": "npm run build && npx vite-bundle-analyzer dist"
  }
}
```

### Caching Strategy
- **Static Assets** - 1 year cache with immutable
- **HTML** - No cache
- **API Responses** - 5 minutes cache
- **Images** - 1 month cache

### CDN Configuration
- **Global Distribution** - Edge locations worldwide
- **Compression** - Gzip/Brotli compression
- **Image Optimization** - WebP conversion
- **HTTP/2** - Multiplexing support
