# Mi<PERSON>i <PERSON>m

## 🏗️ Clean Architecture Overview

### Katman Yapısı
```
src/
├── domain/              # İş mantığı katmanı
│   ├── entities/        # İş varlıkları
│   ├── repositories/    # Repository interfaces
│   └── use-cases/       # İş kuralları
├── application/         # Uygulama katmanı
│   ├── services/        # Uygulama servisleri
│   ├── stores/          # State management
│   └── workflows/       # İş akışları
├── infrastructure/      # Altyapı katmanı
│   ├── repositories/    # Repository implementations
│   ├── services/        # External services
│   └── storage/         # Data persistence
├── presentation/        # Sunum katmanı
│   ├── components/      # React components
│   ├── pages/          # Page components
│   ├── hooks/          # Custom hooks
│   └── contexts/       # React contexts
└── shared/             # Paylaşılan katman
    ├── types/          # Type definitions
    ├── constants/      # Sabitler
    └── utils/          # Utility functions
```

### Dependency Rule
- **D<PERSON><PERSON> katmanlar** iç katmanlara bağımlı olabilir
- **<PERSON><PERSON> katmanlar** d<PERSON><PERSON> katmanlara bağımlı OLAMAZ
- **Domain** hi<PERSON><PERSON> katmana bağımlı değil
- **Application** sadece Domain'e bağımlı
- **Infrastructure** Application ve Domain'e bağımlı
- **Presentation** tüm katmanlara bağımlı olabilir

## 🎯 Domain Katmanı

### Entities
```typescript
// Project Entity
export class Project {
  constructor(
    public readonly id: ProjectId,
    public name: string,
    public description: string,
    private layers: Layer[],
    private rules: Rule[],
    private settings: ProjectSettings
  ) {}

  addLayer(layer: Layer): void
  removeLayer(layerId: LayerId): void
  reorderLayers(layerIds: LayerId[]): void
  addRule(rule: Rule): void
  validateRules(): ValidationResult
}

// Layer Entity
export class Layer {
  constructor(
    public readonly id: LayerId,
    public name: string,
    public order: number,
    private traits: Trait[],
    private rarityConstraint?: RarityConstraint
  ) {}

  addTrait(trait: Trait): void
  removeTrait(traitId: TraitId): void
  calculateTotalRarity(): number
  validateRarity(): boolean
}

// Trait Entity
export class Trait {
  constructor(
    public readonly id: TraitId,
    public name: string,
    public layerId: LayerId,
    public imagePath: string,
    public rarity: number,
    public isEnabled: boolean = true
  ) {}

  updateRarity(newRarity: number): void
  enable(): void
  disable(): void
}
```

### Value Objects
```typescript
// IDs
export class ProjectId extends ValueObject<string> {}
export class LayerId extends ValueObject<string> {}
export class TraitId extends ValueObject<string> {}

// Rarity
export class Rarity extends ValueObject<number> {
  constructor(value: number) {
    if (value < 0 || value > 100) {
      throw new Error('Rarity must be between 0 and 100')
    }
    super(value)
  }
}

// File Path
export class FilePath extends ValueObject<string> {
  constructor(value: string) {
    if (!this.isValidPath(value)) {
      throw new Error('Invalid file path')
    }
    super(value)
  }
}
```

### Repository Interfaces
```typescript
export interface ProjectRepository {
  save(project: Project): Promise<void>
  findById(id: ProjectId): Promise<Project | null>
  findAll(): Promise<Project[]>
  delete(id: ProjectId): Promise<void>
}

export interface LayerRepository {
  importFromDirectory(path: string): Promise<Layer[]>
  saveImage(trait: Trait, imageData: Blob): Promise<void>
  loadImage(trait: Trait): Promise<Blob | null>
}
```

### Use Cases
```typescript
export class CreateProjectUseCase {
  constructor(
    private projectRepo: ProjectRepository,
    private idGenerator: IdGenerator
  ) {}

  async execute(command: CreateProjectCommand): Promise<Project> {
    const projectId = this.idGenerator.generate()
    const project = new Project(
      projectId,
      command.name,
      command.description,
      [],
      [],
      command.settings
    )
    
    await this.projectRepo.save(project)
    return project
  }
}

export class ImportLayersUseCase {
  constructor(
    private layerRepo: LayerRepository,
    private contentAnalyzer: ContentAnalyzer
  ) {}

  async execute(command: ImportLayersCommand): Promise<ImportResult> {
    const layers = await this.layerRepo.importFromDirectory(command.path)
    const analysis = await this.contentAnalyzer.analyze(layers)
    
    return {
      layers,
      suggestions: analysis.suggestions,
      warnings: analysis.warnings
    }
  }
}
```

## ⚙️ Application Katmanı

### Services
```typescript
export class ProjectService {
  constructor(
    private projectRepo: ProjectRepository,
    private createProjectUseCase: CreateProjectUseCase,
    private updateProjectUseCase: UpdateProjectUseCase
  ) {}

  async createProject(data: CreateProjectData): Promise<Project> {
    return this.createProjectUseCase.execute(data)
  }

  async updateProject(id: ProjectId, updates: ProjectUpdates): Promise<void> {
    return this.updateProjectUseCase.execute({ id, updates })
  }
}

export class GenerationService {
  constructor(
    private rulesEngine: RulesEngine,
    private rarityCalculator: RarityCalculator,
    private duplicateDetector: DuplicateDetector
  ) {}

  async generateNFTs(
    project: Project,
    count: number,
    options: GenerationOptions
  ): Promise<NFT[]> {
    const nfts: NFT[] = []
    
    for (let i = 0; i < count; i++) {
      const traits = await this.selectTraits(project)
      const nft = new NFT(traits)
      
      if (!this.duplicateDetector.isDuplicate(nft, nfts)) {
        nfts.push(nft)
      }
    }
    
    return nfts
  }
}
```

### State Management (Zustand)
```typescript
interface AppState {
  // Project State
  currentProject: Project | null
  projects: Project[]
  
  // UI State
  selectedLayerId: LayerId | null
  selectedTraitIds: TraitId[]
  viewMode: 'grid' | 'list'
  
  // Loading State
  isLoading: boolean
  progress: ProgressInfo | null
  
  // Error State
  errors: AppError[]
}

interface AppActions {
  // Project Actions
  createProject: (data: CreateProjectData) => Promise<void>
  loadProject: (id: ProjectId) => Promise<void>
  saveProject: () => Promise<void>
  
  // Layer Actions
  importLayers: (path: string) => Promise<void>
  reorderLayers: (layerIds: LayerId[]) => void
  
  // Trait Actions
  updateTraitRarity: (traitId: TraitId, rarity: number) => void
  toggleTraitEnabled: (traitId: TraitId) => void
  
  // Generation Actions
  generateNFTs: (count: number) => Promise<NFT[]>
  
  // UI Actions
  setSelectedLayer: (layerId: LayerId | null) => void
  setViewMode: (mode: 'grid' | 'list') => void
}

export const useAppStore = create<AppState & AppActions>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        currentProject: null,
        projects: [],
        selectedLayerId: null,
        selectedTraitIds: [],
        viewMode: 'grid',
        isLoading: false,
        progress: null,
        errors: [],

        // Actions Implementation
        createProject: async (data) => {
          set({ isLoading: true })
          try {
            const project = await projectService.createProject(data)
            set(state => ({
              currentProject: project,
              projects: [...state.projects, project],
              isLoading: false
            }))
          } catch (error) {
            set(state => ({
              errors: [...state.errors, error],
              isLoading: false
            }))
          }
        },
        
        // ... other actions
      }),
      { name: 'app-store' }
    )
  )
)
```

## 🔧 Infrastructure Katmanı

### Repository Implementations
```typescript
export class IndexedDBProjectRepository implements ProjectRepository {
  constructor(private db: IDBDatabase) {}

  async save(project: Project): Promise<void> {
    const transaction = this.db.transaction(['projects'], 'readwrite')
    const store = transaction.objectStore('projects')
    await store.put(project.toJSON())
  }

  async findById(id: ProjectId): Promise<Project | null> {
    const transaction = this.db.transaction(['projects'], 'readonly')
    const store = transaction.objectStore('projects')
    const data = await store.get(id.value)
    return data ? Project.fromJSON(data) : null
  }
}

export class FileSystemLayerRepository implements LayerRepository {
  async importFromDirectory(path: string): Promise<Layer[]> {
    const handle = await window.showDirectoryPicker()
    const layers: Layer[] = []
    
    for await (const [name, fileHandle] of handle.entries()) {
      if (fileHandle.kind === 'directory') {
        const layer = await this.processLayerDirectory(name, fileHandle)
        layers.push(layer)
      }
    }
    
    return layers
  }
}
```

### External Services
```typescript
export class WebWorkerGenerationService {
  private worker: Worker

  constructor() {
    this.worker = new Worker('/workers/generation.worker.js')
  }

  async generateNFTs(
    project: Project,
    count: number
  ): Promise<NFT[]> {
    return new Promise((resolve, reject) => {
      this.worker.postMessage({
        type: 'GENERATE',
        payload: { project: project.toJSON(), count }
      })
      
      this.worker.onmessage = (event) => {
        if (event.data.type === 'GENERATION_COMPLETE') {
          resolve(event.data.payload.nfts)
        } else if (event.data.type === 'GENERATION_ERROR') {
          reject(new Error(event.data.payload.error))
        }
      }
    })
  }
}

export class CanvasImageCompositionService {
  async composeNFT(traits: Trait[]): Promise<Blob> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = 512
    canvas.height = 512
    
    for (const trait of traits) {
      const image = await this.loadImage(trait.imagePath)
      ctx.drawImage(image, 0, 0, 512, 512)
    }
    
    return new Promise(resolve => {
      canvas.toBlob(resolve!, 'image/png')
    })
  }
}
```

## 🎨 Presentation Katmanı

### Component Architecture
```typescript
// Page Components
export const WorkspacePage: React.FC = () => {
  const { currentProject } = useAppStore()
  
  if (!currentProject) {
    return <HomePage />
  }
  
  return (
    <WorkspaceLayout>
      <LayersPanel />
      <TraitsPanel />
      <PreviewPanel />
    </WorkspaceLayout>
  )
}

// Container Components
export const LayersPanel: React.FC = () => {
  const { layers, reorderLayers, importLayers } = useAppStore()
  
  return (
    <Panel title="Layers">
      <LayerImportButton onClick={importLayers} />
      <LayerList
        layers={layers}
        onReorder={reorderLayers}
      />
    </Panel>
  )
}

// Presentation Components
export const LayerList: React.FC<LayerListProps> = ({
  layers,
  onReorder
}) => {
  return (
    <DndContext onDragEnd={handleDragEnd}>
      <SortableContext items={layers}>
        {layers.map(layer => (
          <LayerItem
            key={layer.id}
            layer={layer}
          />
        ))}
      </SortableContext>
    </DndContext>
  )
}
```

### Custom Hooks
```typescript
export const useProjectOperations = () => {
  const store = useAppStore()
  
  const createProject = useCallback(async (data: CreateProjectData) => {
    await store.createProject(data)
  }, [store])
  
  const saveProject = useCallback(async () => {
    await store.saveProject()
  }, [store])
  
  return {
    createProject,
    saveProject,
    isLoading: store.isLoading
  }
}

export const useLayerOperations = () => {
  const store = useAppStore()
  
  const importLayers = useCallback(async () => {
    try {
      await store.importLayers()
    } catch (error) {
      toast.error('Layer import failed')
    }
  }, [store])
  
  return {
    importLayers,
    layers: store.currentProject?.layers || []
  }
}
```

## 🔄 Data Flow

### Command Flow
```
User Action → Component → Hook → Store Action → Service → Use Case → Repository
```

### Query Flow
```
Component → Hook → Store Selector → Derived State → UI Update
```

### Event Flow
```
Domain Event → Event Handler → Store Update → UI Re-render
```

## 🧪 Testing Strategy

### Unit Tests
- **Domain Entities** - Business logic testing
- **Use Cases** - Workflow testing
- **Services** - Integration testing
- **Components** - UI testing

### Integration Tests
- **Store Operations** - State management testing
- **Repository Operations** - Data persistence testing
- **Service Integration** - Cross-service testing

### E2E Tests
- **User Workflows** - Complete scenario testing
- **Performance Tests** - Load and stress testing
- **Browser Compatibility** - Cross-browser testing
