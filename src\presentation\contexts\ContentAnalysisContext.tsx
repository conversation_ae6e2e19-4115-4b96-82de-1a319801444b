import React, { createContext, useContext, useState, ReactNode } from 'react'

interface ContentAnalysisState {
  isAnalyzing: boolean
  results: any[]
  showDialog: boolean
}

interface ContentAnalysisContextType {
  state: ContentAnalysisState
  setAnalyzing: (analyzing: boolean) => void
  setResults: (results: any[]) => void
  setShowDialog: (show: boolean) => void
  startAnalysis: () => void
  finishAnalysis: (results: any[]) => void
}

const ContentAnalysisContext = createContext<ContentAnalysisContextType | undefined>(undefined)

interface ContentAnalysisProviderProps {
  children: ReactNode
}

export const ContentAnalysisProvider: React.FC<ContentAnalysisProviderProps> = ({ children }) => {
  const [state, setState] = useState<ContentAnalysisState>({
    isAnalyzing: false,
    results: [],
    showDialog: false
  })

  const setAnalyzing = (analyzing: boolean) => {
    setState(prev => ({ ...prev, isAnalyzing: analyzing }))
  }

  const setResults = (results: any[]) => {
    setState(prev => ({ ...prev, results }))
  }

  const setShowDialog = (show: boolean) => {
    setState(prev => ({ ...prev, showDialog: show }))
  }

  const startAnalysis = () => {
    setState(prev => ({ ...prev, isAnalyzing: true, results: [] }))
  }

  const finishAnalysis = (results: any[]) => {
    setState(prev => ({ 
      ...prev, 
      isAnalyzing: false, 
      results, 
      showDialog: results.length > 0 
    }))
  }

  const value: ContentAnalysisContextType = {
    state,
    setAnalyzing,
    setResults,
    setShowDialog,
    startAnalysis,
    finishAnalysis
  }

  return (
    <ContentAnalysisContext.Provider value={value}>
      {children}
    </ContentAnalysisContext.Provider>
  )
}

export const useContentAnalysis = (): ContentAnalysisContextType => {
  const context = useContext(ContentAnalysisContext)
  if (context === undefined) {
    throw new Error('useContentAnalysis must be used within a ContentAnalysisProvider')
  }
  return context
}
