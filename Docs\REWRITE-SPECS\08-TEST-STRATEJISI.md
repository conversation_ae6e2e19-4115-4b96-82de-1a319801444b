# Test Stratejisi

## 🎯 Test Hedefleri

### <PERSON><PERSON>
- **Test Coverage:** %90+ overall coverage
- **Unit Test Coverage:** %95+ for business logic
- **Integration Test Coverage:** %85+ for critical paths
- **E2E Test Coverage:** 100% for user workflows

### Performance Hedefleri
- **Test Execution Time:** <5 dakika (full suite)
- **Unit Tests:** <30 saniye
- **Integration Tests:** <2 dakika
- **E2E Tests:** <10 dakika

## 🏗️ Test Piramidi

```
        E2E Tests (5%)
       ┌─────────────┐
      │  User Flows  │
     └─────────────────┘
    
    Integration Tests (25%)
   ┌─────────────────────┐
  │  Component + Store   │
 │   Service + Repository │
└─────────────────────────┘

      Unit Tests (70%)
┌─────────────────────────────┐
│  Components, Hooks, Utils   │
│  Domain Logic, Services     │
│  Pure Functions, Validators │
└─────────────────────────────┘
```

## 🧪 Test Türleri

### 1. Unit Tests

#### Domain Entity Tests
```typescript
// Project.test.ts
describe('Project Entity', () => {
  describe('constructor', () => {
    it('should create project with valid data', () => {
      const project = new Project({
        name: 'Test Project',
        description: 'Test Description'
      })
      
      expect(project.name).toBe('Test Project')
      expect(project.description).toBe('Test Description')
      expect(project.layers).toEqual([])
      expect(project.rules).toEqual([])
    })
    
    it('should throw error with invalid name', () => {
      expect(() => {
        new Project({ name: '', description: 'Test' })
      }).toThrow('Project name cannot be empty')
    })
  })
  
  describe('addLayer', () => {
    it('should add layer to project', () => {
      const project = new Project({ name: 'Test', description: 'Test' })
      const layer = new Layer({ name: 'Background', order: 0 })
      
      project.addLayer(layer)
      
      expect(project.layers).toHaveLength(1)
      expect(project.layers[0]).toBe(layer)
    })
    
    it('should maintain layer order', () => {
      const project = new Project({ name: 'Test', description: 'Test' })
      const layer1 = new Layer({ name: 'Background', order: 0 })
      const layer2 = new Layer({ name: 'Character', order: 1 })
      
      project.addLayer(layer2)
      project.addLayer(layer1)
      
      expect(project.layers[0]).toBe(layer1)
      expect(project.layers[1]).toBe(layer2)
    })
  })
})
```

#### Component Tests
```typescript
// LayerItem.test.tsx
describe('LayerItem Component', () => {
  const mockLayer = {
    id: 'layer-1',
    name: 'Background',
    order: 0,
    isVisible: true,
    isLocked: false,
    traits: []
  }
  
  it('renders layer information', () => {
    render(<LayerItem layer={mockLayer} />)
    
    expect(screen.getByText('Background')).toBeInTheDocument()
    expect(screen.getByLabelText('Toggle visibility')).toBeInTheDocument()
    expect(screen.getByLabelText('Toggle lock')).toBeInTheDocument()
  })
  
  it('calls onToggleVisibility when visibility button clicked', () => {
    const onToggleVisibility = jest.fn()
    render(
      <LayerItem
        layer={mockLayer}
        onToggleVisibility={onToggleVisibility}
      />
    )
    
    fireEvent.click(screen.getByLabelText('Toggle visibility'))
    expect(onToggleVisibility).toHaveBeenCalledWith('layer-1')
  })
  
  it('shows drag handle when draggable', () => {
    render(<LayerItem layer={mockLayer} draggable />)
    
    expect(screen.getByLabelText('Drag handle')).toBeInTheDocument()
  })
})
```

#### Hook Tests
```typescript
// useProjectOperations.test.ts
describe('useProjectOperations Hook', () => {
  beforeEach(() => {
    useAppStore.setState({
      projects: [],
      currentProject: null,
      isLoading: false
    })
  })
  
  it('creates project successfully', async () => {
    const { result } = renderHook(() => useProjectOperations())
    
    await act(async () => {
      await result.current.createProject({
        name: 'Test Project',
        description: 'Test Description'
      })
    })
    
    const state = useAppStore.getState()
    expect(state.projects).toHaveLength(1)
    expect(state.currentProject?.name).toBe('Test Project')
  })
  
  it('handles create project error', async () => {
    // Mock service to throw error
    jest.spyOn(projectService, 'createProject')
      .mockRejectedValue(new Error('Creation failed'))
    
    const { result } = renderHook(() => useProjectOperations())
    
    await act(async () => {
      await result.current.createProject({
        name: 'Test Project',
        description: 'Test Description'
      })
    })
    
    const state = useAppStore.getState()
    expect(state.errors).toHaveLength(1)
    expect(state.errors[0].message).toBe('Creation failed')
  })
})
```

### 2. Integration Tests

#### Store Integration Tests
```typescript
// appStore.integration.test.ts
describe('App Store Integration', () => {
  beforeEach(() => {
    useAppStore.getState().reset()
  })
  
  it('completes full project workflow', async () => {
    const store = useAppStore.getState()
    
    // Create project
    await store.createProject({
      name: 'Integration Test Project',
      description: 'Test Description'
    })
    
    expect(store.currentProject).toBeTruthy()
    expect(store.projects).toHaveLength(1)
    
    // Import layers
    await store.importLayers('/test/layers')
    
    expect(store.currentProject?.layers).toHaveLength(3)
    
    // Update trait rarity
    const firstTrait = store.currentProject?.layers[0].traits[0]
    if (firstTrait) {
      store.updateTraitRarity(firstTrait.id, 25.5)
      expect(firstTrait.rarity).toBe(25.5)
    }
    
    // Generate NFTs
    const nfts = await store.generateNFTs(10)
    
    expect(nfts).toHaveLength(10)
    expect(store.generatedNFTs).toHaveLength(10)
  })
})
```

#### Service Integration Tests
```typescript
// layerImportService.integration.test.ts
describe('Layer Import Service Integration', () => {
  it('imports layers from directory structure', async () => {
    const mockFileSystem = createMockFileSystem({
      'Background': ['bg1.png', 'bg2.png'],
      'Character': {
        'Male': ['male1.png', 'male2.png'],
        'Female': ['female1.png', 'female2.png']
      }
    })
    
    const result = await layerImportService.importFromDirectory(
      mockFileSystem
    )
    
    expect(result.success).toBe(true)
    expect(result.layers).toHaveLength(2)
    
    const backgroundLayer = result.layers.find(l => l.name === 'Background')
    expect(backgroundLayer?.traits).toHaveLength(2)
    
    const characterLayer = result.layers.find(l => l.name === 'Character')
    expect(characterLayer?.traitGroups).toHaveLength(2)
    expect(characterLayer?.traits).toHaveLength(4)
  })
})
```

### 3. E2E Tests

#### User Workflow Tests
```typescript
// userWorkflow.e2e.test.ts
describe('Complete User Workflow', () => {
  it('creates NFT collection from start to finish', async () => {
    // Navigate to app
    await page.goto('http://localhost:3000')
    
    // Create new project
    await page.click('[data-testid="create-project-button"]')
    await page.fill('[data-testid="project-name-input"]', 'E2E Test Project')
    await page.click('[data-testid="create-button"]')
    
    // Verify project created
    await expect(page.locator('[data-testid="project-title"]'))
      .toHaveText('E2E Test Project')
    
    // Import layers
    await page.click('[data-testid="import-layers-button"]')
    await page.setInputFiles(
      '[data-testid="folder-input"]',
      './test-assets/layers'
    )
    
    // Wait for import to complete
    await expect(page.locator('[data-testid="layers-list"]'))
      .toContainText('Background')
    
    // Adjust trait rarities
    await page.click('[data-testid="trait-grid-item"]:first-child')
    await page.fill('[data-testid="rarity-input"]', '25.5')
    await page.press('[data-testid="rarity-input"]', 'Enter')
    
    // Generate NFTs
    await page.click('[data-testid="generate-button"]')
    await page.fill('[data-testid="generation-count"]', '10')
    await page.click('[data-testid="start-generation"]')
    
    // Wait for generation to complete
    await expect(page.locator('[data-testid="generation-progress"]'))
      .toHaveText('100%')
    
    // Verify NFTs generated
    await expect(page.locator('[data-testid="nft-grid"]'))
      .toContainText('10 NFTs generated')
    
    // Export NFTs
    await page.click('[data-testid="export-button"]')
    await page.click('[data-testid="export-all"]')
    
    // Verify export started
    await expect(page.locator('[data-testid="export-status"]'))
      .toContainText('Exporting...')
  })
})
```

## 🛠️ Test Utilities

### Test Setup
```typescript
// test-utils.tsx
export const renderWithProviders = (
  ui: React.ReactElement,
  options: RenderOptions = {}
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <ThemeProvider theme={testTheme}>
      <QueryClientProvider client={testQueryClient}>
        {children}
      </QueryClientProvider>
    </ThemeProvider>
  )
  
  return render(ui, { wrapper: Wrapper, ...options })
}

export const createMockProject = (overrides: Partial<Project> = {}): Project => ({
  id: 'test-project-1',
  name: 'Test Project',
  description: 'Test Description',
  createdAt: new Date(),
  updatedAt: new Date(),
  layers: [],
  rules: [],
  settings: createMockProjectSettings(),
  ...overrides
})

export const createMockLayer = (overrides: Partial<Layer> = {}): Layer => ({
  id: 'test-layer-1',
  name: 'Test Layer',
  order: 0,
  isVisible: true,
  isLocked: false,
  traits: [],
  traitGroups: [],
  ...overrides
})
```

### Mock Services
```typescript
// mocks/services.ts
export const mockProjectService = {
  createProject: jest.fn(),
  updateProject: jest.fn(),
  deleteProject: jest.fn(),
  loadProject: jest.fn()
}

export const mockLayerImportService = {
  importFromDirectory: jest.fn(),
  validateFiles: jest.fn(),
  processImages: jest.fn()
}

export const mockGenerationService = {
  generateNFTs: jest.fn(),
  calculateRarity: jest.fn(),
  validateGeneration: jest.fn()
}
```

## 📊 Test Coverage

### Coverage Targets
```typescript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.tsx',
    '!src/test-utils/**'
  ],
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './src/domain/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  }
}
```

### Coverage Reports
- **HTML Report** - Detailed coverage visualization
- **LCOV Report** - CI/CD integration
- **JSON Report** - Programmatic access
- **Text Summary** - Console output

## 🚀 Test Automation

### CI/CD Pipeline
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### Test Scripts
```json
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run --coverage",
    "test:integration": "vitest run --config vitest.integration.config.ts",
    "test:e2e": "playwright test",
    "test:watch": "vitest --watch",
    "test:ui": "vitest --ui"
  }
}
```

## 🔍 Test Monitoring

### Metrics Tracking
- **Test Execution Time** - Performance monitoring
- **Flaky Test Detection** - Reliability tracking
- **Coverage Trends** - Quality trends
- **Test Failure Analysis** - Root cause analysis

### Quality Gates
- **All tests must pass** - No failing tests
- **Coverage threshold** - Minimum coverage requirements
- **Performance threshold** - Maximum execution time
- **No flaky tests** - Consistent test results
