# Component Tasarımı

## 🎨 Component Hiyerarşisi

### App Level Components
```
App
├── ThemeProvider
├── ErrorBoundary
├── QueryClientProvider
└── Router
    ├── HomePage
    └── WorkspacePage
        ├── WorkspaceLayout
        │   ├── Header
        │   └── PanelContainer
        │       ├── LayersPanel
        │       ├── TraitsPanel
        │       └── PreviewPanel
        └── Modals
            ├── SettingsModal
            ├── RulesModal
            └── ExportModal
```

### Panel Components
```
LayersPanel
├── PanelHeader
│   ├── PanelTitle
│   └── ImportButton
├── LayerList
│   └── LayerItem[]
│       ├── LayerIcon
│       ├── LayerName
│       ├── LayerControls
│       └── TraitGroupList
└── PanelFooter

TraitsPanel
├── PanelHeader
│   ├── PanelTitle
│   ├── ViewToggle
│   └── DistributeButtons
├── TraitGrid
│   └── TraitItem[]
│       ├── TraitImage
│       ├── TraitName
│       └── RarityChip
└── PanelFooter

PreviewPanel
├── PanelHeader
│   ├── PanelTitle
│   ├── RandomizeButton
│   └── ExportButton
├── PreviewCanvas
│   └── LayerStack[]
├── MetadataDisplay
└── PanelFooter
```

## 🧩 Core Components

### Layout Components

#### WorkspaceLayout
```typescript
interface WorkspaceLayoutProps {
  children: React.ReactNode
}

export const WorkspaceLayout: React.FC<WorkspaceLayoutProps> = ({
  children
}) => {
  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Header />
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {children}
      </Box>
    </Box>
  )
}
```

#### PanelContainer
```typescript
interface PanelContainerProps {
  children: [React.ReactNode, React.ReactNode, React.ReactNode] // 3 panels
}

export const PanelContainer: React.FC<PanelContainerProps> = ({
  children
}) => {
  const [panelSizes, setPanelSizes] = useLocalStorage('panelSizes', [25, 35, 40])
  
  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel defaultSize={panelSizes[0]}>
        {children[0]}
      </ResizablePanel>
      <ResizableHandle />
      <ResizablePanel defaultSize={panelSizes[1]}>
        {children[1]}
      </ResizablePanel>
      <ResizableHandle />
      <ResizablePanel defaultSize={panelSizes[2]}>
        {children[2]}
      </ResizablePanel>
    </ResizablePanelGroup>
  )
}
```

### Panel Components

#### Panel (Base Component)
```typescript
interface PanelProps {
  title: string
  children: React.ReactNode
  actions?: React.ReactNode
  footer?: React.ReactNode
}

export const Panel: React.FC<PanelProps> = ({
  title,
  children,
  actions,
  footer
}) => {
  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <PanelHeader title={title} actions={actions} />
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {children}
      </Box>
      {footer && <PanelFooter>{footer}</PanelFooter>}
    </Paper>
  )
}
```

#### LayersPanel
```typescript
export const LayersPanel: React.FC = () => {
  const { layers, importLayers, reorderLayers } = useLayerOperations()
  
  const handleImport = useCallback(async () => {
    await importLayers()
  }, [importLayers])
  
  return (
    <Panel
      title="Layers"
      actions={
        <ImportButton onClick={handleImport} />
      }
    >
      <LayerList
        layers={layers}
        onReorder={reorderLayers}
      />
    </Panel>
  )
}
```

#### TraitsPanel
```typescript
export const TraitsPanel: React.FC = () => {
  const {
    traits,
    viewMode,
    setViewMode,
    distributeEvenly,
    distributeRandomly
  } = useTraitOperations()
  
  return (
    <Panel
      title="Traits"
      actions={
        <Box sx={{ display: 'flex', gap: 1 }}>
          <ViewToggle value={viewMode} onChange={setViewMode} />
          <DistributeButton
            variant="evenly"
            onClick={distributeEvenly}
          />
          <DistributeButton
            variant="randomly"
            onClick={distributeRandomly}
          />
        </Box>
      }
    >
      <TraitGrid
        traits={traits}
        viewMode={viewMode}
      />
    </Panel>
  )
}
```

#### PreviewPanel
```typescript
export const PreviewPanel: React.FC = () => {
  const {
    selectedTraits,
    randomizePreview,
    exportNFT
  } = usePreviewOperations()
  
  return (
    <Panel
      title="Preview"
      actions={
        <Box sx={{ display: 'flex', gap: 1 }}>
          <RandomizeButton onClick={randomizePreview} />
          <ExportButton onClick={exportNFT} />
        </Box>
      }
    >
      <PreviewCanvas traits={selectedTraits} />
      <MetadataDisplay traits={selectedTraits} />
    </Panel>
  )
}
```

## 🎯 Specialized Components

### Layer Components

#### LayerItem
```typescript
interface LayerItemProps {
  layer: Layer
  onReorder?: (layerId: string, newOrder: number) => void
  onToggleVisibility?: (layerId: string) => void
  onToggleLock?: (layerId: string) => void
}

export const LayerItem: React.FC<LayerItemProps> = ({
  layer,
  onReorder,
  onToggleVisibility,
  onToggleLock
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: layer.id })
  
  return (
    <Box
      ref={setNodeRef}
      sx={{
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1
      }}
      {...attributes}
      {...listeners}
    >
      <Card sx={{ mb: 1 }}>
        <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DragIndicatorIcon />
          <LayerIcon type={layer.type} />
          <Typography variant="body2" sx={{ flex: 1 }}>
            {layer.name}
          </Typography>
          <IconButton
            size="small"
            onClick={() => onToggleVisibility?.(layer.id)}
          >
            {layer.isVisible ? <VisibilityIcon /> : <VisibilityOffIcon />}
          </IconButton>
          <IconButton
            size="small"
            onClick={() => onToggleLock?.(layer.id)}
          >
            {layer.isLocked ? <LockIcon /> : <LockOpenIcon />}
          </IconButton>
        </CardContent>
        {layer.hasTraitGroups && (
          <Collapse in={layer.isExpanded}>
            <TraitGroupList groups={layer.traitGroups} />
          </Collapse>
        )}
      </Card>
    </Box>
  )
}
```

### Trait Components

#### TraitGrid
```typescript
interface TraitGridProps {
  traits: Trait[]
  viewMode: 'grid' | 'list'
  onTraitSelect?: (traitId: string) => void
  onRarityChange?: (traitId: string, rarity: number) => void
}

export const TraitGrid: React.FC<TraitGridProps> = ({
  traits,
  viewMode,
  onTraitSelect,
  onRarityChange
}) => {
  if (viewMode === 'list') {
    return (
      <List>
        {traits.map(trait => (
          <TraitListItem
            key={trait.id}
            trait={trait}
            onSelect={onTraitSelect}
            onRarityChange={onRarityChange}
          />
        ))}
      </List>
    )
  }
  
  return (
    <Grid container spacing={1}>
      {traits.map(trait => (
        <Grid item xs={6} sm={4} md={3} key={trait.id}>
          <TraitGridItem
            trait={trait}
            onSelect={onTraitSelect}
            onRarityChange={onRarityChange}
          />
        </Grid>
      ))}
    </Grid>
  )
}
```

#### TraitGridItem
```typescript
interface TraitGridItemProps {
  trait: Trait
  onSelect?: (traitId: string) => void
  onRarityChange?: (traitId: string, rarity: number) => void
}

export const TraitGridItem: React.FC<TraitGridItemProps> = ({
  trait,
  onSelect,
  onRarityChange
}) => {
  const [isEditing, setIsEditing] = useState(false)
  
  return (
    <Card
      sx={{
        cursor: 'pointer',
        '&:hover': { boxShadow: 2 }
      }}
      onClick={() => onSelect?.(trait.id)}
    >
      <CardMedia
        component="img"
        height="120"
        image={trait.imagePath}
        alt={trait.name}
        sx={{ objectFit: 'cover' }}
      />
      <CardContent sx={{ p: 1 }}>
        <Typography variant="caption" noWrap>
          {trait.name}
        </Typography>
        <EditableRarityChip
          value={trait.rarity}
          onChange={(value) => onRarityChange?.(trait.id, value)}
        />
      </CardContent>
    </Card>
  )
}
```

### Preview Components

#### PreviewCanvas
```typescript
interface PreviewCanvasProps {
  traits: Trait[]
  width?: number
  height?: number
}

export const PreviewCanvas: React.FC<PreviewCanvasProps> = ({
  traits,
  width = 512,
  height = 512
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')!
    ctx.clearRect(0, 0, width, height)
    
    // Render traits in layer order
    traits
      .sort((a, b) => a.layer.order - b.layer.order)
      .forEach(async (trait) => {
        const image = await loadImage(trait.imagePath)
        ctx.drawImage(image, 0, 0, width, height)
      })
  }, [traits, width, height])
  
  return (
    <Box sx={{ textAlign: 'center', p: 2 }}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        style={{
          maxWidth: '100%',
          height: 'auto',
          border: '1px solid #ccc',
          borderRadius: 8
        }}
      />
    </Box>
  )
}
```

## 🎛️ Control Components

### Buttons

#### ImportButton
```typescript
interface ImportButtonProps {
  onClick: () => void
  loading?: boolean
}

export const ImportButton: React.FC<ImportButtonProps> = ({
  onClick,
  loading = false
}) => {
  return (
    <Button
      variant="outlined"
      size="small"
      startIcon={loading ? <CircularProgress size={16} /> : <FolderIcon />}
      onClick={onClick}
      disabled={loading}
    >
      {loading ? 'Importing...' : 'Import'}
    </Button>
  )
}
```

#### DistributeButton
```typescript
interface DistributeButtonProps {
  variant: 'evenly' | 'randomly'
  onClick: () => void
}

export const DistributeButton: React.FC<DistributeButtonProps> = ({
  variant,
  onClick
}) => {
  const icon = variant === 'evenly' ? <BalanceIcon /> : <CasinoIcon />
  const label = variant === 'evenly' ? 'Distribute Evenly' : 'Distribute Randomly'
  
  return (
    <Tooltip title={label}>
      <IconButton size="small" onClick={onClick}>
        {icon}
      </IconButton>
    </Tooltip>
  )
}
```

### Input Components

#### EditableRarityChip
```typescript
interface EditableRarityChipProps {
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
}

export const EditableRarityChip: React.FC<EditableRarityChipProps> = ({
  value,
  onChange,
  min = 0,
  max = 100
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [inputValue, setInputValue] = useState(value.toFixed(2))
  
  const handleSave = () => {
    const numValue = parseFloat(inputValue)
    if (!isNaN(numValue) && numValue >= min && numValue <= max) {
      onChange(numValue)
    }
    setIsEditing(false)
  }
  
  if (isEditing) {
    return (
      <TextField
        size="small"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={(e) => e.key === 'Enter' && handleSave()}
        autoFocus
        sx={{ width: 80 }}
      />
    )
  }
  
  return (
    <Chip
      label={`${value.toFixed(2)}%`}
      size="small"
      onClick={() => setIsEditing(true)}
      sx={{ cursor: 'pointer' }}
    />
  )
}
```

## 🎨 Theme Integration

### Component Styling
```typescript
// Theme-aware component styling
export const StyledPanel = styled(Paper)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  
  '& .panel-header': {
    padding: theme.spacing(1, 2),
    borderBottom: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.default
  },
  
  '& .panel-content': {
    flex: 1,
    overflow: 'auto',
    padding: theme.spacing(1)
  }
}))
```

### Responsive Design
```typescript
// Responsive breakpoints
export const useResponsiveLayout = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'))
  
  return {
    isMobile,
    isTablet,
    panelLayout: isMobile ? 'stack' : 'horizontal',
    gridColumns: isMobile ? 2 : isTablet ? 3 : 4
  }
}
```

## 🧪 Component Testing

### Test Structure
```typescript
describe('LayerItem', () => {
  const mockLayer = {
    id: 'layer-1',
    name: 'Background',
    isVisible: true,
    isLocked: false,
    order: 0
  }
  
  it('renders layer information correctly', () => {
    render(<LayerItem layer={mockLayer} />)
    
    expect(screen.getByText('Background')).toBeInTheDocument()
    expect(screen.getByLabelText('Visibility')).toBeInTheDocument()
    expect(screen.getByLabelText('Lock')).toBeInTheDocument()
  })
  
  it('calls onToggleVisibility when visibility button clicked', () => {
    const onToggleVisibility = jest.fn()
    render(
      <LayerItem
        layer={mockLayer}
        onToggleVisibility={onToggleVisibility}
      />
    )
    
    fireEvent.click(screen.getByLabelText('Visibility'))
    expect(onToggleVisibility).toHaveBeenCalledWith('layer-1')
  })
})
```
