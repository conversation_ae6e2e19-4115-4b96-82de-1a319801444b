import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Box, Typography, Button, Paper } from '@mui/material'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined })
  }

  public render() {
    if (this.state.hasError) {
      return (
        <Box
          sx={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 3,
            backgroundColor: '#1a1a1a'
          }}
        >
          <Paper
            sx={{
              padding: 4,
              maxWidth: 600,
              textAlign: 'center',
              backgroundColor: '#2d2d2d'
            }}
          >
            <Typography variant="h4" color="error" gutterBottom>
              ⚠️ Bir Hata Oluştu
            </Typography>
            
            <Typography variant="body1" sx={{ marginBottom: 3, color: '#b0b0b0' }}>
              Uygulama beklenmeyen bir hatayla karşılaştı. Lütfen sayfayı yenileyin veya tekrar deneyin.
            </Typography>

            {this.state.error && (
              <Typography 
                variant="body2" 
                sx={{ 
                  marginBottom: 3, 
                  padding: 2, 
                  backgroundColor: '#1a1a1a',
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.8rem',
                  color: '#ff6b6b'
                }}
              >
                {this.state.error.message}
              </Typography>
            )}

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button 
                variant="contained" 
                onClick={this.handleReset}
                sx={{ backgroundColor: '#667eea' }}
              >
                Tekrar Dene
              </Button>
              
              <Button 
                variant="outlined" 
                onClick={() => window.location.reload()}
                sx={{ borderColor: '#667eea', color: '#667eea' }}
              >
                Sayfayı Yenile
              </Button>
            </Box>
          </Paper>
        </Box>
      )
    }

    return this.props.children
  }
}
