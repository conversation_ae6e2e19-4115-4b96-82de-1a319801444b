import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Basit tip tanımları
interface Project {
  id: string
  name: string
  description?: string
  createdAt: number
  updatedAt: number
}

interface AppError {
  code: string
  message: string
  timestamp: Date
  context?: string
  details?: any
  id?: string
}

interface AppStore {
  // State
  currentProject: Project | null
  projects: Project[]
  errors: AppError[]
  isLoading: boolean

  // Actions
  createProject: (data: { name: string; description?: string; createdAt: number; updatedAt: number }) => void
  loadProject: (projectId: string) => void
  addError: (error: AppError) => void
  removeError: (errorId: string) => void
  clearErrors: () => void
  setLoading: (loading: boolean) => void
  reset: () => void
}

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      // Initial State
      currentProject: null,
      projects: [],
      errors: [],
      isLoading: false,

      // Actions
      createProject: (data) => {
        const project: Project = {
          id: `project_${Date.now()}`,
          ...data
        }

        set((state) => ({
          currentProject: project,
          projects: [...state.projects, project]
        }))
      },

      loadProject: (projectId: string) => {
        const { projects } = get()
        const project = projects.find(p => p.id === projectId)
        if (project) {
          set({ currentProject: project })
        } else {
          get().addError({
            code: 'PROJECT_NOT_FOUND',
            message: `Project with ID ${projectId} not found`,
            timestamp: new Date(),
            context: 'loadProject'
          })
        }
      },

      addError: (error: AppError) => {
        set((state) => ({
          errors: [...state.errors, { ...error, id: `error_${Date.now()}` }]
        }))
      },

      removeError: (errorId: string) => {
        set((state) => ({
          errors: state.errors.filter(e => e.id !== errorId)
        }))
      },

      clearErrors: () => {
        set({ errors: [] })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      reset: () => {
        set({
          currentProject: null,
          projects: [],
          errors: [],
          isLoading: false
        })
      }
    }),
    { name: 'app-store' }
  )
)
