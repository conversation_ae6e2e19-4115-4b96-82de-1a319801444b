# Veri Modeli

## 🏗️ Entity Relationships

```mermaid
erDiagram
    Project ||--o{ Layer : contains
    Project ||--o{ Rule : has
    Project ||--|| ProjectSettings : has
    Layer ||--o{ Trait : contains
    Layer ||--o{ TraitGroup : has
    TraitGroup ||--o{ Trait : contains
    Rule ||--o{ RuleCondition : has
    Trait ||--o{ TraitMetadata : has
    
    Project {
        string id PK
        string name
        string description
        datetime createdAt
        datetime updatedAt
        ProjectSettings settings
    }
    
    Layer {
        string id PK
        string projectId FK
        string name
        number order
        boolean isVisible
        boolean isLocked
        RarityConstraint rarityConstraint
    }
    
    Trait {
        string id PK
        string layerId FK
        string traitGroupId FK
        string name
        string imagePath
        number rarity
        boolean isEnabled
        TraitMetadata metadata
    }
    
    Rule {
        string id PK
        string projectId FK
        string name
        string description
        RuleType type
        boolean enabled
        number priority
    }
```

## 📊 Core Entities

### Project Entity
```typescript
export interface Project {
  readonly id: ProjectId
  name: string
  description: string
  readonly createdAt: Date
  updatedAt: Date
  layers: Layer[]
  rules: Rule[]
  settings: ProjectSettings
}

export interface ProjectSettings {
  canvasSize: {
    width: number
    height: number
  }
  outputFormat: 'png' | 'jpg' | 'webp'
  outputQuality: number // 0-100
  backgroundColor: string
  exportSettings: {
    includeMetadata: boolean
    metadataFormat: 'json' | 'csv'
    filenamePattern: string
  }
}

// Project Factory
export class ProjectFactory {
  static create(data: CreateProjectData): Project {
    return {
      id: generateId(),
      name: data.name,
      description: data.description || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      layers: [],
      rules: [],
      settings: this.getDefaultSettings()
    }
  }
  
  static getDefaultSettings(): ProjectSettings {
    return {
      canvasSize: { width: 512, height: 512 },
      outputFormat: 'png',
      outputQuality: 100,
      backgroundColor: 'transparent',
      exportSettings: {
        includeMetadata: true,
        metadataFormat: 'json',
        filenamePattern: '{id}_{name}'
      }
    }
  }
}
```

### Layer Entity
```typescript
export interface Layer {
  readonly id: LayerId
  readonly projectId: ProjectId
  name: string
  order: number
  isVisible: boolean
  isLocked: boolean
  traits: Trait[]
  traitGroups: TraitGroup[]
  rarityConstraint?: RarityConstraint
  metadata: LayerMetadata
}

export interface RarityConstraint {
  maxRarity: number // 0-100
  isLocked: boolean
  distribution: 'even' | 'weighted' | 'custom'
}

export interface LayerMetadata {
  importPath: string
  fileCount: number
  totalSize: number
  lastModified: Date
  layerType: LayerType
}

export type LayerType = 
  | 'background'
  | 'base'
  | 'clothing'
  | 'accessory'
  | 'face'
  | 'hair'
  | 'eyes'
  | 'mouth'
  | 'overlay'
  | 'effect'

// Layer Operations
export class LayerOperations {
  static calculateTotalRarity(layer: Layer): number {
    return layer.traits.reduce((sum, trait) => sum + trait.rarity, 0)
  }
  
  static validateRarity(layer: Layer): ValidationResult {
    const total = this.calculateTotalRarity(layer)
    const constraint = layer.rarityConstraint
    
    if (constraint && total > constraint.maxRarity) {
      return {
        isValid: false,
        errors: [`Total rarity (${total}%) exceeds constraint (${constraint.maxRarity}%)`]
      }
    }
    
    return { isValid: true, errors: [] }
  }
  
  static distributeRarityEvenly(layer: Layer): Layer {
    const traitCount = layer.traits.length
    const rarityPerTrait = 100 / traitCount
    
    return {
      ...layer,
      traits: layer.traits.map(trait => ({
        ...trait,
        rarity: rarityPerTrait
      }))
    }
  }
}
```

### Trait Entity
```typescript
export interface Trait {
  readonly id: TraitId
  readonly layerId: LayerId
  traitGroupId?: TraitGroupId
  name: string
  imagePath: string
  rarity: number // 0-100
  isEnabled: boolean
  metadata: TraitMetadata
}

export interface TraitMetadata {
  fileSize: number
  dimensions: {
    width: number
    height: number
  }
  format: string
  checksum: string
  importedAt: Date
  tags: string[]
}

export interface TraitGroup {
  readonly id: TraitGroupId
  readonly layerId: LayerId
  name: string
  traits: Trait[]
  isExpanded: boolean
  rarityConstraint?: RarityConstraint
}

// Trait Operations
export class TraitOperations {
  static updateRarity(trait: Trait, newRarity: number): Trait {
    if (newRarity < 0 || newRarity > 100) {
      throw new Error('Rarity must be between 0 and 100')
    }
    
    return {
      ...trait,
      rarity: newRarity
    }
  }
  
  static enable(trait: Trait): Trait {
    return { ...trait, isEnabled: true }
  }
  
  static disable(trait: Trait): Trait {
    return { ...trait, isEnabled: false }
  }
  
  static calculateEffectiveRarity(
    trait: Trait,
    layer: Layer
  ): number {
    if (!trait.isEnabled) return 0
    
    const constraint = layer.rarityConstraint
    if (!constraint) return trait.rarity
    
    const totalRarity = LayerOperations.calculateTotalRarity(layer)
    const scaleFactor = constraint.maxRarity / totalRarity
    
    return trait.rarity * scaleFactor
  }
}
```

### Rule Entity
```typescript
export interface Rule {
  readonly id: RuleId
  readonly projectId: ProjectId
  name: string
  description: string
  type: RuleType
  enabled: boolean
  priority: number
  conditions: RuleCondition[]
  actions: RuleAction[]
  metadata: RuleMetadata
}

export type RuleType = 
  | 'IF_THEN'
  | 'LAYER_GROUP'
  | 'TRAIT_GROUP'
  | 'EXCLUSION'
  | 'DEPENDENCY'

export interface RuleCondition {
  readonly id: ConditionId
  type: 'IF' | 'THEN'
  layerId: LayerId
  traitId?: TraitId
  operator: ConditionOperator
  logicalOperator?: 'AND' | 'OR'
}

export type ConditionOperator = 
  | 'MUST_HAVE'
  | 'CANNOT_HAVE'
  | 'PREFER'
  | 'AVOID'

export interface RuleAction {
  readonly id: ActionId
  type: ActionType
  targetLayerId: LayerId
  targetTraitId?: TraitId
  parameters: Record<string, any>
}

export type ActionType = 
  | 'FORCE_SELECT'
  | 'FORCE_EXCLUDE'
  | 'ADJUST_RARITY'
  | 'GROUP_SELECT'

export interface RuleMetadata {
  createdAt: Date
  updatedAt: Date
  appliedCount: number
  conflictCount: number
  lastApplied?: Date
}

// Rule Operations
export class RuleOperations {
  static evaluate(
    rule: Rule,
    selectedTraits: Trait[]
  ): RuleEvaluationResult {
    if (!rule.enabled) {
      return { matches: false, actions: [] }
    }
    
    const conditionResults = rule.conditions.map(condition => 
      this.evaluateCondition(condition, selectedTraits)
    )
    
    const matches = this.evaluateLogicalExpression(
      conditionResults,
      rule.conditions
    )
    
    return {
      matches,
      actions: matches ? rule.actions : []
    }
  }
  
  static detectConflicts(rules: Rule[]): RuleConflict[] {
    const conflicts: RuleConflict[] = []
    
    for (let i = 0; i < rules.length; i++) {
      for (let j = i + 1; j < rules.length; j++) {
        const conflict = this.checkRuleConflict(rules[i], rules[j])
        if (conflict) {
          conflicts.push(conflict)
        }
      }
    }
    
    return conflicts
  }
}
```

## 🎯 Generated NFT Model

### NFT Entity
```typescript
export interface NFT {
  readonly id: NFTId
  readonly projectId: ProjectId
  name: string
  traits: SelectedTrait[]
  rarity: RarityScore
  metadata: NFTMetadata
  imageData?: Blob
  generatedAt: Date
}

export interface SelectedTrait {
  readonly traitId: TraitId
  readonly layerId: LayerId
  name: string
  layerName: string
  rarity: number
  imagePath: string
}

export interface RarityScore {
  total: number
  rank: number
  percentile: number
  breakdown: TraitRarityBreakdown[]
}

export interface TraitRarityBreakdown {
  traitId: TraitId
  traitName: string
  layerName: string
  rarity: number
  contribution: number // Contribution to total rarity
}

export interface NFTMetadata {
  name: string
  description: string
  image: string
  attributes: MetadataAttribute[]
  properties: Record<string, any>
  compiler: string
  date: number
}

export interface MetadataAttribute {
  trait_type: string
  value: string
  rarity?: number
}

// NFT Operations
export class NFTOperations {
  static calculateRarity(
    nft: NFT,
    allNFTs: NFT[]
  ): RarityScore {
    const traitRarities = nft.traits.map(trait => {
      const occurrences = allNFTs.filter(otherNFT =>
        otherNFT.traits.some(t => t.traitId === trait.traitId)
      ).length
      
      const rarity = (occurrences / allNFTs.length) * 100
      
      return {
        traitId: trait.traitId,
        traitName: trait.name,
        layerName: trait.layerName,
        rarity,
        contribution: 100 - rarity
      }
    })
    
    const totalRarity = traitRarities.reduce(
      (sum, tr) => sum + tr.contribution, 0
    )
    
    const rank = this.calculateRank(nft, allNFTs)
    const percentile = (rank / allNFTs.length) * 100
    
    return {
      total: totalRarity,
      rank,
      percentile,
      breakdown: traitRarities
    }
  }
  
  static generateMetadata(nft: NFT): NFTMetadata {
    return {
      name: nft.name,
      description: `Generated NFT #${nft.id}`,
      image: `${nft.id}.png`,
      attributes: nft.traits.map(trait => ({
        trait_type: trait.layerName,
        value: trait.name,
        rarity: trait.rarity
      })),
      properties: {
        rarity_rank: nft.rarity.rank,
        rarity_score: nft.rarity.total
      },
      compiler: 'NFT Generator Pro V2',
      date: nft.generatedAt.getTime()
    }
  }
}
```

## 🔄 State Management Models

### App State
```typescript
export interface AppState {
  // Project State
  currentProject: Project | null
  projects: Project[]
  
  // UI State
  ui: UIState
  
  // Generation State
  generation: GenerationState
  
  // System State
  system: SystemState
}

export interface UIState {
  selectedLayerId: LayerId | null
  selectedTraitIds: TraitId[]
  selectedTraitGroupId: TraitGroupId | null
  viewMode: 'grid' | 'list'
  filterText: string
  sortBy: 'name' | 'rarity' | 'order'
  sortDirection: 'asc' | 'desc'
  showHidden: boolean
  expandedLayers: LayerId[]
  panelSizes: [number, number, number]
}

export interface GenerationState {
  isGenerating: boolean
  progress: GenerationProgress | null
  generatedNFTs: NFT[]
  selectedNFTs: NFTId[]
  previewNFT: NFT | null
}

export interface SystemState {
  isLoading: boolean
  errors: AppError[]
  notifications: Notification[]
  performance: PerformanceMetrics
}
```

### Store Actions
```typescript
export interface AppActions {
  // Project Actions
  createProject: (data: CreateProjectData) => Promise<void>
  loadProject: (id: ProjectId) => Promise<void>
  saveProject: () => Promise<void>
  updateProject: (updates: Partial<Project>) => void
  deleteProject: (id: ProjectId) => Promise<void>
  
  // Layer Actions
  importLayers: (path: string) => Promise<void>
  addLayer: (layer: Layer) => void
  updateLayer: (id: LayerId, updates: Partial<Layer>) => void
  deleteLayer: (id: LayerId) => void
  reorderLayers: (layerIds: LayerId[]) => void
  
  // Trait Actions
  updateTrait: (id: TraitId, updates: Partial<Trait>) => void
  updateTraitRarity: (id: TraitId, rarity: number) => void
  toggleTraitEnabled: (id: TraitId) => void
  distributeRarityEvenly: (layerId: LayerId) => void
  distributeRarityRandomly: (layerId: LayerId) => void
  
  // Rule Actions
  addRule: (rule: Rule) => void
  updateRule: (id: RuleId, updates: Partial<Rule>) => void
  deleteRule: (id: RuleId) => void
  toggleRuleEnabled: (id: RuleId) => void
  
  // Generation Actions
  generateNFTs: (count: number, options: GenerationOptions) => Promise<void>
  randomizePreview: () => void
  selectNFT: (id: NFTId) => void
  exportNFTs: (ids: NFTId[], options: ExportOptions) => Promise<void>
  
  // UI Actions
  setSelectedLayer: (id: LayerId | null) => void
  setSelectedTraits: (ids: TraitId[]) => void
  setViewMode: (mode: 'grid' | 'list') => void
  setFilter: (text: string) => void
  setSorting: (by: string, direction: 'asc' | 'desc') => void
  
  // System Actions
  addError: (error: AppError) => void
  removeError: (id: string) => void
  addNotification: (notification: Notification) => void
  removeNotification: (id: string) => void
}
```

## 💾 Persistence Models

### Storage Schema
```typescript
export interface StorageSchema {
  projects: {
    key: ProjectId
    value: Project
    indexes: {
      name: string
      createdAt: Date
      updatedAt: Date
    }
  }
  
  images: {
    key: string // trait image path
    value: {
      data: ArrayBuffer
      metadata: ImageMetadata
      cachedAt: Date
    }
  }
  
  settings: {
    key: string
    value: any
  }
  
  cache: {
    key: string
    value: {
      data: any
      expiresAt: Date
    }
  }
}

export interface ImageMetadata {
  width: number
  height: number
  format: string
  size: number
  checksum: string
}
```

### Migration Schema
```typescript
export interface MigrationSchema {
  version: number
  migrations: Migration[]
}

export interface Migration {
  version: number
  description: string
  up: (db: IDBDatabase) => Promise<void>
  down: (db: IDBDatabase) => Promise<void>
}
```
