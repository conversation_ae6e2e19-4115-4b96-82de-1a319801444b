import React from 'react'
import { Box, Typography, Button, CssBaseline } from '@mui/material'

function App() {
  console.log('🎉 App component rendering...')

  return (
    <>
      <CssBaseline />
      <Box 
        sx={{ 
          minHeight: '100vh', 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        <Typography 
          variant="h2" 
          component="h1" 
          sx={{ 
            color: 'white', 
            textAlign: 'center', 
            marginBottom: 3,
            fontWeight: 'bold'
          }}
        >
          🎨 NFT Generator Pro v2
        </Typography>
        
        <Typography 
          variant="h5" 
          sx={{ 
            color: 'white', 
            textAlign: 'center', 
            marginBottom: 4,
            opacity: 0.9
          }}
        >
          Proje başarıyla çalışıyor!
        </Typography>

        <Button 
          variant="contained" 
          size="large"
          sx={{ 
            backgroundColor: 'white',
            color: '#667eea',
            '&:hover': {
              backgroundColor: '#f5f5f5'
            }
          }}
          onClick={() => {
            console.log('🎉 Test button clicked!')
            alert('NFT Generator Pro v2 çalışıyor!')
          }}
        >
          Test Et
        </Button>

        <Typography 
          variant="body2" 
          sx={{ 
            color: 'white', 
            textAlign: 'center', 
            marginTop: 3,
            opacity: 0.7
          }}
        >
          Console'u kontrol edin (F12) - herhangi bir hata var mı?
        </Typography>
      </Box>
    </>
  )
}

export default App
