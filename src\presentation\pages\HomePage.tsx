import React from 'react'
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Button, 
  Paper, 
  Container,
  Grid,
  Card,
  CardContent,
  CardActions
} from '@mui/material'
import { Add as AddIcon, Folder as FolderIcon } from '@mui/icons-material'
import { useAppStore } from '../../application/stores/appStore'

export const HomePage: React.FC = () => {
  const { createProject, projects } = useAppStore()

  const handleCreateProject = () => {
    const projectName = `NFT Project ${Date.now()}`
    createProject({
      name: projectName,
      description: 'Yeni NFT projesi',
      createdAt: Date.now(),
      updatedAt: Date.now()
    })
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography 
          variant="h2" 
          component="h1" 
          sx={{ 
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2
          }}
        >
          🎨 NFT Generator Pro v2
        </Typography>
        
        <Typography variant="h5" color="text.secondary" sx={{ mb: 4 }}>
          Profesyonel NFT koleksiyonları oluşturun
        </Typography>

        <Button
          variant="contained"
          size="large"
          startIcon={<AddIcon />}
          onClick={handleCreateProject}
          sx={{
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            '&:hover': {
              background: 'linear-gradient(45deg, #5a6fd8, #6a4190)',
            }
          }}
        >
          Yeni Proje Oluştur
        </Button>
      </Box>

      {/* Projects Grid */}
      {projects.length > 0 && (
        <Box>
          <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
            Projelerim
          </Typography>
          
          <Grid container spacing={3}>
            {projects.map((project) => (
              <Grid item xs={12} sm={6} md={4} key={project.id}>
                <Card 
                  sx={{ 
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <FolderIcon sx={{ mr: 1, color: '#667eea' }} />
                      <Typography variant="h6" component="h3">
                        {project.name}
                      </Typography>
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {project.description || 'Açıklama yok'}
                    </Typography>
                    
                    <Typography variant="caption" color="text.secondary">
                      Oluşturulma: {new Date(project.createdAt).toLocaleDateString('tr-TR')}
                    </Typography>
                  </CardContent>
                  
                  <CardActions>
                    <Button 
                      size="small" 
                      variant="contained"
                      onClick={() => {
                        // Project'i aç
                        console.log('Opening project:', project.id)
                      }}
                      sx={{ 
                        background: 'linear-gradient(45deg, #667eea, #764ba2)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #5a6fd8, #6a4190)',
                        }
                      }}
                    >
                      Aç
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Empty State */}
      {projects.length === 0 && (
        <Paper 
          sx={{ 
            p: 6, 
            textAlign: 'center',
            backgroundColor: 'background.paper',
            border: '2px dashed',
            borderColor: 'divider'
          }}
        >
          <FolderIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" color="text.secondary" sx={{ mb: 2 }}>
            Henüz proje yok
          </Typography>
          <Typography variant="body1" color="text.secondary">
            İlk NFT projenizi oluşturmak için yukarıdaki butona tıklayın
          </Typography>
        </Paper>
      )}
    </Container>
  )
}
