import React from 'react'
import { CssBaseline, Box, Typography, Button } from '@mui/material'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in v5)
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        <Typography
          variant="h2"
          component="h1"
          sx={{
            color: 'white',
            textAlign: 'center',
            marginBottom: 3,
            fontWeight: 'bold'
          }}
        >
          🎨 NFT Generator Pro v2
        </Typography>

        <Typography
          variant="h5"
          sx={{
            color: 'white',
            textAlign: 'center',
            marginBottom: 4,
            opacity: 0.9
          }}
        >
          Proje başarıyla çalışıyor!
        </Typography>

        <Button
          variant="contained"
          size="large"
          sx={{
            backgroundColor: 'white',
            color: '#667eea',
            '&:hover': {
              backgroundColor: '#f5f5f5'
            }
          }}
          onClick={() => {
            console.log('🎉 NFT Generator Pro v2 is running!')
            alert('Proje çalışıyor! Eksik component\'ler eklendikten sonra tam işlevsellik sağlanacak.')
          }}
        >
          Test Et
        </Button>
      </Box>
    </QueryClientProvider>
  )
}

export default App
