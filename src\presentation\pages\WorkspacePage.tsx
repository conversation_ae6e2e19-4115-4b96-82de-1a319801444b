import React from 'react'
import { Box, Typography, Paper, Button } from '@mui/material'
import { 
  Layers as LayersIcon,
  GridView as GridIcon,
  Preview as PreviewIcon,
  Settings as SettingsIcon,
  Rule as RuleIcon
} from '@mui/icons-material'

export const WorkspacePage: React.FC = () => {
  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Top Toolbar */}
      <Paper 
        sx={{ 
          p: 2, 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderRadius: 0,
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          NFT Generator Pro v2 - Workspace
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            size="small"
          >
            Settings
          </Button>
          <Button
            variant="outlined"
            startIcon={<RuleIcon />}
            size="small"
          >
            Rules
          </Button>
          <Button
            variant="contained"
            sx={{ 
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              '&:hover': {
                background: 'linear-gradient(45deg, #5a6fd8, #6a4190)',
              }
            }}
          >
            Generate NFTs
          </Button>
        </Box>
      </Paper>

      {/* 3-Panel Layout */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Left Panel - Layers */}
        <Paper 
          sx={{ 
            width: '25%', 
            m: 1, 
            p: 2, 
            display: 'flex', 
            flexDirection: 'column',
            borderRadius: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LayersIcon sx={{ mr: 1, color: '#667eea' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Layers
            </Typography>
          </Box>
          
          <Box sx={{ 
            flex: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            border: '2px dashed',
            borderColor: 'divider',
            borderRadius: 2,
            flexDirection: 'column',
            gap: 2
          }}>
            <LayersIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Layer'ları import etmek için<br />klasör seçin
            </Typography>
            <Button variant="outlined" size="small">
              Import Layers
            </Button>
          </Box>
        </Paper>

        {/* Center Panel - Traits */}
        <Paper 
          sx={{ 
            width: '35%', 
            m: 1, 
            p: 2, 
            display: 'flex', 
            flexDirection: 'column',
            borderRadius: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <GridIcon sx={{ mr: 1, color: '#667eea' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Traits
            </Typography>
          </Box>
          
          <Box sx={{ 
            flex: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            border: '2px dashed',
            borderColor: 'divider',
            borderRadius: 2,
            flexDirection: 'column',
            gap: 2
          }}>
            <GridIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Trait'ler burada görünecek<br />
              Layer import ettikten sonra
            </Typography>
          </Box>
        </Paper>

        {/* Right Panel - Preview */}
        <Paper 
          sx={{ 
            width: '40%', 
            m: 1, 
            p: 2, 
            display: 'flex', 
            flexDirection: 'column',
            borderRadius: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <PreviewIcon sx={{ mr: 1, color: '#667eea' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Preview
            </Typography>
          </Box>
          
          <Box sx={{ 
            flex: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            border: '2px dashed',
            borderColor: 'divider',
            borderRadius: 2,
            flexDirection: 'column',
            gap: 2
          }}>
            <PreviewIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              NFT önizlemesi burada görünecek<br />
              Trait'ler seçildikten sonra
            </Typography>
            <Button variant="outlined" size="small">
              Randomize
            </Button>
          </Box>
        </Paper>
      </Box>
    </Box>
  )
}
